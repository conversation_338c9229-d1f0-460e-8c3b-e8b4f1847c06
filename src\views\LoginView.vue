<template>
  <div class="login-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.5 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 4" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
    </div>
    
    <van-form @submit="onSubmit" class="form-wrapper">
      <div class="header">
        <div class="logo-container">
          <div class="medical-icon">⚕️</div>
          <h2>医疗报告系统</h2>
          <p class="subtitle">Medical Report System</p>
        </div>
      </div>

      <van-field
        v-model="username"
        name="用户名"
        label="用户名"
        placeholder="请输入用户名"
        :rules="[{ required: true, message: '请填写用户名' }]"
        left-icon="user-o"
      />
      
      <van-field
        v-model="password"
        type="password"
        name="密码"
        label="密码"
        placeholder="请输入密码"
        :rules="[{ required: true, message: '请填写密码' }]"
        left-icon="lock"
      />

      <van-field
        v-model="captcha"
        center
        clearable
        placeholder="请输入验证码"
        :rules="[{ required: true, message: '请填写验证码' }]"
      >
        <template #button>
          <div class="captcha-container" @click="refreshCaptcha">
            <canvas ref="captchaCanvas" width="100" height="30"></canvas>
          </div>
        </template>
      </van-field>

      <div class="login-actions">
        <van-button 
          round 
          block 
          type="primary"
          native-type="submit"
          :loading="loading"
          loading-text="登录中..."
          class="login-btn"
        >
          立即登录
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { useRouter } from 'vue-router';
import { login } from '@/api/auth';
import CryptoJS from 'crypto-js';
import { useStore } from 'vuex'

const router = useRouter();
const username = ref('');
const password = ref('');
const captcha = ref('');
const captchaCanvas = ref(null);
const loading = ref(false);
const currentCaptcha = ref('');

// 生成验证码
const generateCaptcha = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
  let result = '';
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 绘制验证码
const drawCaptcha = () => {
  if (!captchaCanvas.value) return;
  
  const canvas = captchaCanvas.value;
  const ctx = canvas.getContext('2d');
  
  // 清空画布
  ctx.clearRect(0, 0, 100, 38);
  
  // 设置背景
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, 100, 38);
  
  // 生成验证码文本
  currentCaptcha.value = generateCaptcha();
  
  // 绘制验证码文字
  ctx.font = '18px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  for (let i = 0; i < currentCaptcha.value.length; i++) {
    ctx.fillStyle = `hsl(${Math.random() * 360}, 70%, 50%)`;
    const x = 20 + i * 15;
    const y = 19 + (Math.random() - 0.5) * 6;
    const angle = (Math.random() - 0.5) * 0.3;
    
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);
    ctx.fillText(currentCaptcha.value[i], 0, 0);
    ctx.restore();
  }
  
  // 添加干扰线
  for (let i = 0; i < 3; i++) {
    ctx.strokeStyle = `hsl(${Math.random() * 360}, 50%, 70%)`;
    ctx.beginPath();
    ctx.moveTo(Math.random() * 100, Math.random() * 38);
    ctx.lineTo(Math.random() * 100, Math.random() * 38);
    ctx.stroke();
  }
};

const refreshCaptcha = () => {
  drawCaptcha();
};

const store = useStore();

const onSubmit = async () => {
  // 验证码校验
  if (captcha.value.toLowerCase() !== currentCaptcha.value.toLowerCase()) {
    showFailToast('验证码错误');
    refreshCaptcha();
    captcha.value = '';
    return;
  }
  
  try {
    loading.value = true;
    const hashedPassword = CryptoJS.SHA256(password.value).toString(CryptoJS.enc.Hex);
    const response = await login({
      username: username.value,
      password: hashedPassword
    });
    const data = response.data;
    
    // 根据实际返回的数据格式调整判断逻辑
    if (data.status === 'success') {
      store.commit('SET_TOKEN', data.token);
      store.commit('SET_USERINFO', data.userInfo);
      // 记录登录时间戳
      localStorage.setItem('loginTime', Date.now().toString());
      router.replace('/home').catch(()=>{});
    } else {
      showFailToast(data.message || '登录失败');
      refreshCaptcha();
      captcha.value = '';
    }
  } catch (error) {
    console.error('登录错误:', error);
    showFailToast('网络异常');
    refreshCaptcha();
    captcha.value = '';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  drawCaptcha();
});
</script>

<style scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 动态背景元素 */
.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 24px;
  font-weight: bold;
  animation: float 6s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 10%; left: 10%; }
.floating-cross:nth-child(2) { top: 20%; right: 15%; }
.floating-cross:nth-child(3) { top: 60%; left: 5%; }
.floating-cross:nth-child(4) { bottom: 20%; right: 10%; }
.floating-cross:nth-child(5) { top: 40%; left: 80%; }
.floating-cross:nth-child(6) { bottom: 40%; left: 20%; }

.floating-circle {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: pulse 4s ease-in-out infinite;
}

.floating-circle:nth-child(7) { top: 15%; left: 60%; }
.floating-circle:nth-child(8) { top: 70%; right: 30%; }
.floating-circle:nth-child(9) { bottom: 30%; left: 70%; }
.floating-circle:nth-child(10) { top: 50%; right: 5%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.1; }
  50% { transform: scale(1.2); opacity: 0.3; }
}

.form-wrapper {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.95);
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-container {
  position: relative;
}

.medical-icon {
  font-size: 48px;
  margin-bottom: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header h2 {
  color: #2c5aa0;
  font-size: 28px;
  font-weight: 600;
  margin: 10px 0 5px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
  font-weight: 300;
}

:deep(.van-field) {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.van-field__label) {
  color: #2c5aa0;
  font-weight: 500;
}

:deep(.van-field__control) {
  font-size: 16px;
}

.captcha-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.captcha-container:hover {
  background: #e9ecef;
  transform: scale(1.02);
}

.refresh-hint {
  font-size: 10px;
  color: #6c757d;
  margin-top: 2px;
}

.login-actions {
  margin-top: 30px;
}

.login-btn {
  background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
  border: none;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
}

:deep(.van-button--loading) {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .form-wrapper {
    padding: 30px 20px;
    margin: 0 10px;
  }
  
  .header h2 {
    font-size: 24px;
  }
  
  .medical-icon {
    font-size: 40px;
  }
}
</style>